import { authMiddleware } from '@clerk/nextjs';

export const config = {
  matcher: '/((?!_next/image|_next/static|favicon.ico|.*.svg).*)'
};

export default authMiddleware({
  debug: false,
  publicRoutes: ['/', '/api/auth/me', '/sign-in', '/sign-up', '/onboarding-installation'],
  ignoredRoutes: [
    '/api/data-collector-api',
    '/api/stdout-capture',
    '/assets/:path*', // captures all assets
    '/api/error-db/identification',
    'api/upload/presigned-put',
    '/api/logs-forward/dev',
    '/api/logs-forward/prod',
    '/assets/brand/:path*',
    '/fonts/:path*', // captures all fonts
    '/fonts/Roboto/:path*',
    '/fonts/Roboto/Roboto-Regular.ttf',
    '/fonts/Roboto/Roboto-Bold.ttf',
    '/assets/product-screenshot-workspace.png',
    '/assets/hero-demo.png',
    '/assets/video-thumbnail.png',
    '/assets/netrunner-prouduct-screenshot.png',
    '/assets/brand/netrunner-main-character.png',
    '/apple-touch-icon.png',
    '/apple-touch-icon-precomposed.png',
    '/.well-known/appspecific/com.chrome.devtools.json',
    '/onboarding-installation'
  ]
});
