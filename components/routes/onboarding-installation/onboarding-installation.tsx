// components/onboarding-installation.js

import React from 'react'

/**************************************
 * StepSection
 * - Displays a heading and wraps its children.
 **************************************/
export function StepSection({ title, children }) {
  return (
    <div className="mb-8">
      <h2 className="text-xl font-medium">{title}</h2>
      <div className="mt-2">{children}</div>
    </div>
  )
}

/**************************************
 * OptionCard
 * - A selectable card with an icon and label.
 * - `selected` controls border color.
 **************************************/
export function OptionCard({ icon, label, selected, onClick }) {
  return (
    <div
      onClick={onClick}
      className={`
        cursor-pointer
        border rounded-lg p-4 flex flex-col items-center justify-center
        ${selected ? 'border-blue-500 border-2' : 'border-gray-700 border'}
        w-32 h-24
      `}
    >
      <div className="mb-2">
        {icon}
      </div>
      <span className="text-sm">{label}</span>
    </div>
  )
}
