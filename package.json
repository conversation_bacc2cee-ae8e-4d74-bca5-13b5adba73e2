{"name": "tracer-web-client", "author": "<PERSON>", "version": "0.1.1", "private": true, "dependencies": {"@aws-sdk/client-s3": "^3.534.0", "@aws-sdk/s3-request-presigner": "^3.534.0", "@chakra-ui/next-js": "^2.2.0", "@chakra-ui/react": "~2.8.2", "@clerk/nextjs": "^4.29.9", "@clickhouse/client": "^1.1.0", "@emotion/react": "^11.11.4", "@emotion/styled": "^11.11.0", "@fortawesome/fontawesome-svg-core": "^6.5.1", "@fortawesome/free-brands-svg-icons": "^6.5.1", "@fortawesome/free-solid-svg-icons": "^6.5.1", "@fortawesome/react-fontawesome": "^0.2.0", "@headlessui/react": "^1.7.18", "@heroicons/react": "^1.0.6", "@mui/material": "^5.15.13", "@radix-ui/colors": "^0.1.9", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-popover": "^1.0.7", "@radix-ui/react-progress": "^1.0.3", "@radix-ui/react-scroll-area": "^1.0.5", "@radix-ui/react-select": "^1.2.2", "@radix-ui/react-slot": "^1.0.2", "@stitches/react": "^1.2.8", "@stripe/react-stripe-js": "^2.6.2", "@stripe/stripe-js": "^1.54.2", "@tailwindcss/aspect-ratio": "^0.4.2", "@tanstack/react-table": "^8.13.2", "@tremor/react": "^3.16.0", "@types/pg": "^8.12.0", "@xata.io/client": "^0.29.3", "axios": "^1.6.8", "canvas-confetti": "^1.9.3", "chartjs-adapter-date-fns": "^3.0.0", "chartjs-adapter-luxon": "^1.3.1", "chartjs-plugin-streaming": "^2.0.0", "chartjs-plugin-zoom": "^2.0.1", "class-variance-authority": "^0.7.0", "clsx": "^1.2.1", "date-fns": "^3.6.0", "dayjs": "^1.11.10", "eslint-config-next": "^14.1.3", "eslint-config-prettier": "^8.10.0", "form-data": "^4.0.0", "framer-motion": "^10.18.0", "konva": "^8.4.3", "lodash": "^4.17.21", "logrocket": "^8.1.2", "lucide-react": "^0.358.0", "luxon": "^3.4.4", "mailgun.js": "^10.2.1", "moment": "^2.30.1", "nanoid": "^5.0.6", "next": "15.4.0-canary.61", "openai": "^4.29.0", "pg": "^8.15.6", "react": "^18.2.0", "react-day-picker": "^8.10.0", "react-dom": "^18.2.0", "react-hook-form": "^7.51.0", "react-konva": "^18.2.10", "react-konva-utils": "^0.3.2", "react-syntax-highlighter": "^15.5.0", "reactflow": "^11.11.2", "sass": "^1.77.8", "stripe": "^12.18.0", "styled-components": "^6.1.8", "swr": "^2.2.5", "tailwind-merge": "^2.2.1", "tailwindcss-animate": "^1.0.7", "use-image": "^1.1.1", "uuid": "^9.0.1"}, "scripts": {"preinstall": "npx only-allow pnpm", "dev": "next dev", "build": "next build", "start": "next start", "prettier-format": "prettier --config .prettierrc '**/*.ts' '**/*.js' --write", "check-licenses": "license-checker --production --json --out licenses.json"}, "eslintConfig": {"extends": "react-app"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@tailwindcss/forms": "^0.5.7", "@types/node": "^18.19.24", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@typescript-eslint/eslint-plugin": "^5.62.0", "@typescript-eslint/parser": "^5.62.0", "eslint": "^8.57.0", "eslint-plugin-emotion": "^10.0.27", "prettier": "^2.8.8", "tailwindcss": "^3.4.3", "typescript": "^5.5.4"}}